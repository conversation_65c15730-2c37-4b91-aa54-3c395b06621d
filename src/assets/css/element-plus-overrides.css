/* Element Plus 组件统一样式覆盖 */

/* 表格样式 */
.el-table {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.el-table .el-table__cell {
  text-align: center;
  font-size: 13px;
  border-right: 1px solid var(--border-secondary);
}

.el-table th {
  background-color: var(--bg-primary) !important;
  color: var(--primary-color) !important;
  border-bottom: 2px solid var(--border-primary);
  font-weight: bold;
  font-size: 14px;
}

.el-table td {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-secondary);
  text-align: center;
  font-size: 13px;
}

.el-table tr:hover td {
  background-color: var(--border-primary) !important;
}

/* 按钮样式 */
.el-button {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
}

.el-button--danger {
  background: linear-gradient(135deg, #f56c6c 0%, #e53e3e 100%);
  border-color: #f56c6c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.el-button--danger:hover {
  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
  border-color: #f78989;
}

.el-button--warning {
  background: linear-gradient(135deg, #e6a23c 0%, #d48806 100%);
  border-color: #e6a23c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.el-button--warning:hover {
  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%);
  border-color: #ebb563;
}

.el-button.is-disabled {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
}

.el-button--small {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: var(--radius-sm);
}

/* 输入框样式 */
.el-input__wrapper {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
}

.el-input__inner {
  color: var(--text-primary);
}

.el-input__wrapper:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 12px var(--shadow-primary);
}

.el-input__prefix,
.el-input__suffix {
  color: var(--text-muted);
}

/* 分页样式 */
.el-pagination {
  color: var(--text-primary);
}

.el-pagination .el-pager li {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  margin: 0 2px;
  transition: var(--transition-normal);
}

.el-pagination .el-pager li:hover {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

.el-pagination .el-pager li.is-active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

.el-pagination .btn-prev,
.el-pagination .btn-next {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

.el-pagination .el-select .el-select__wrapper {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  height: 32px;
}

.el-pagination .el-select .el-select__placeholder {
  color: var(--text-primary);
}

/* 对话框样式 */
.el-dialog {
  background: var(--bg-primary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.el-dialog__header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.el-dialog__title {
  color: var(--primary-color);
  font-weight: bold;
}

.el-dialog__body {
  color: var(--text-primary);
}

.el-dialog__footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* 选择器样式 */
.el-select__wrapper {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.el-select__placeholder {
  color: var(--text-muted);
}

/* 树形控件样式 */
.el-tree {
  background: transparent;
  color: var(--text-primary);
}

.el-tree-node__content {
  color: var(--text-primary);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.el-tree-node__content:hover {
  background-color: var(--bg-tertiary);
}

.el-tree-node__content.is-current {
  background-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* 消息框样式 - 简洁清晰 */
.el-message-box {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.el-message-box__header {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
  padding: 20px;
}

.el-message-box__title {
  color: #303133;
  font-weight: 600;
  font-size: 16px;
}

.el-message-box__content {
  padding: 20px;
  background: #ffffff;
}

.el-message-box__message {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.el-message-box__input .el-input__wrapper {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 40px;
}

.el-message-box__input .el-input__wrapper:hover {
  border-color: #c0c4cc;
}

.el-message-box__input .el-input__wrapper.is-focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.el-message-box__input .el-input__inner {
  color: #303133;
  font-size: 14px;
}

.el-message-box__input .el-input__inner::placeholder {
  color: #c0c4cc;
}

.el-message-box__btns {
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  border-radius: 0 0 8px 8px;
  padding: 15px 20px;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.el-message-box__btns .el-button {
  min-width: 80px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.el-message-box__btns .el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

.el-message-box__btns .el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.el-message-box__btns .el-button--default {
  background: #ffffff;
  border-color: #dcdfe6;
  color: #606266;
}

.el-message-box__btns .el-button--default:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.el-message-box__errormsg {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

