<template>
  <div class="manager">
    <HeaderComponent 
      title="管理员模式" 
      :showBackButton="true"
      @back="goBack"
    />
    <video autoplay muted loop id="background-video">
      <source src="../assets/video/sky.mp4" type="video/mp4">
      您的浏览器不支持 video 标签。
    </video>
    <!-- 模式选择按钮 -->
    <div class="content">
      <div class="mode-buttons">
        <div class="tab" @click="topage('sample')">
          <div class="ui-tab-icon ui-sample"></div>
          <div class="ui-tab-title">
            <span>席位一：</span>
            <span>样本管理</span>
          </div>
        </div>
        <div class="tab" @click="topage('detected')">
          <div class="ui-tab-icon ui-dected"></div>
          <div class="ui-tab-title">
            <span>席位二：</span>
            <span>智能判读</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'

export default {
  name: 'ManagerView',
  components: {
    HeaderComponent
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    topage(route) {
      this.$router.push(`/${route}`)
    }
  }
}
</script>

<style scoped>
/* 主容器样式 */
.manager {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 背景视频优化 */
#background-video {
  position: fixed;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  transform: translate(-50%, -50%);
  object-fit: cover;
  will-change: transform;
  backface-visibility: hidden;
}

/* 主要内容区域 */
.content {
  position: relative;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

/* 模式按钮容器 */
.mode-buttons {
  display: flex;
  gap: 300px;
  align-items: center;
  justify-content: center;
}

/* 单个按钮样式 - 性能优化 */
.tab {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 260px;
  height: 329px;
  color: #fff;
  text-align: center;
  border-radius: 10px;
  background-image: url("../assets/imgs/home-item.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.tab:hover {
  transform: scale(1.05) translateZ(0);
}

/* 按钮图标样式 */
.ui-tab-icon {
  width: 110px;
  height: 101px;
  margin-bottom: 20px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  will-change: transform;
  backface-visibility: hidden;
}

.ui-dected {
  background-image: url("../assets/imgs/detected.png");
}

.ui-sample {
  background-image: url("../assets/imgs/sample.png");
}

/* 按钮标题样式 */
.ui-tab-title {
  font-size: 24px;
  color: #a2cfff;
  font-weight: bold;
  letter-spacing: 2px;
  display: flex;
  flex-direction: column;
  line-height: 1.3;
  gap: 5px;
}

.ui-tab-title span {
  display: block;
  white-space: nowrap;
}

/* 修复：将 ::v-deep 替换为 :deep() */
:deep(.header) {
  position: relative;
  z-index: 10;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
    gap: 40px;
  }
  
  .tab {
    width: 220px;
    height: 280px;
  }
  
  .ui-tab-icon {
    width: 90px;
    height: 85px;
    margin-bottom: 15px;
  }
  
  .ui-tab-title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .mode-buttons {
    gap: 30px;
  }
  
  .tab {
    width: 180px;
    height: 240px;
  }
  
  .ui-tab-icon {
    width: 70px;
    height: 65px;
    margin-bottom: 10px;
  }
  
  .ui-tab-title {
    font-size: 18px;
  }
}

/* 性能优化 */
*,
*::before,
*::after {
  box-sizing: border-box;
}
</style>


