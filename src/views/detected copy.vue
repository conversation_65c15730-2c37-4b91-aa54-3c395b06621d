<template>
  <div class="detected">
    <HeaderComponent title="智能判读" :showBackButton="true" @back="goBack" />
    <div class="content">
      <div class="layout">
        <div class="left">
          <div class="panel top-left">
            <div class="panel-title">
              <span>数据输入</span>
              <div>
                <el-select v-model="selectedInputType" @change="selectInputType" class="type-select">
                  <el-option v-for="type in inputTypes" :key="type.value" :label="type.label" :value="type.value">
                    <span class="option-content">
                      <i class="option-icon">{{ type.icon }}</i>
                      {{ type.label }}
                    </span>
                  </el-option>
                </el-select>
                <el-button type="primary" @click="triggerFileUpload">上传</el-button>
              </div>
            </div>
            <div class="panel-body">
              <input type="file" ref="imageInput" style="display: none;" accept="image/*" @change="handleImageUpload" />
              <input type="file" ref="folderInput" style="display: none;" webkitdirectory @change="handleFilesUpload" />
              <input type="file" ref="videoInput" style="display: none;" accept="video/*" @change="handleVideoUpload" />
              <!-- 顶部(80%) 预览区 -->
              <div class="panel-body-top">
                <div class="preview-area">
                  <!-- 图像预览 -->
                  <div v-if="selectedInputType === 'image' && imagePreview" class="preview-block">
                    <img :src="imagePreview" alt="图像预览" class="preview-img" />
                  </div>
                  <!-- 视频预览 -->
                  <div v-else-if="selectedInputType === 'video' && videoPreview" class="preview-block">
                    <video :src="videoPreview" class="preview-video" controls preload="metadata"></video>
                  </div>
                  <!-- 实时视频预览（依赖后端转码为浏览器可播放的流，如HLS/WebRTC/FLV） -->
                  <div v-else-if="selectedInputType === 'realtime' && realtimeStreamUrl" class="preview-block">
                    <!-- HLS 使用 hls.js 播放 -->
                    <video v-if="isHls" ref="realtimeVideo" class="preview-video" controls autoplay muted
                      playsinline></video>
                    <!-- 非 HLS 直接用原生 video 播放，例如 MP4、HTTP-FLV（取决于浏览器） -->
                    <video v-else :src="realtimeStreamUrl" class="preview-video" controls autoplay muted
                      playsinline></video>
                  </div>
                  <!-- 文件夹预览（当前图像） -->
                  <div v-else-if="selectedInputType === 'folder' && folderPreviews.length" class="preview-block">
                    <img :src="currentFolderPreview" alt="文件夹图像预览" class="preview-img" />
                  </div>
                  <!-- 占位提示 -->
                  <div v-else class="empty-state">
                    <div class="empty-content">
                      <i class="empty-icon">📂</i>
                      <h3 class="empty-title">请选择数据源</h3>
                      <p class="empty-description">选择输入类型并上传文件开始分析</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 底部(20%) 信息/操作区 -->
              <div class="panel-body-bottom">
                <!-- 左侧：信息 -->
                <div class="info-left">
                  <!-- 图像信息 -->
                  <div v-if="selectedInputType === 'image' && imageMeta" class="info-row">
                    <span class="info-label">文件：</span><span class="info-text">{{ imageMeta.name }}</span>
                    <span class="info-sep">|</span>
                    <span class="info-label">大小：</span><span class="info-text">{{ formatSize(imageMeta.size) }}</span>
                  </div>
                  <!-- 视频信息 -->
                  <div v-else-if="selectedInputType === 'video' && videoMeta" class="info-row">
                    <span class="info-label">文件：</span><span class="info-text">{{ videoMeta.name }}</span>
                    <span class="info-sep">|</span>
                    <span class="info-label">大小：</span><span class="info-text">{{ formatSize(videoMeta.size) }}</span>
                  </div>
                  <!-- 实时信息 -->
                  <div v-else-if="selectedInputType === 'realtime'" class="info-row">
                    <span class="info-label">RTSP：</span>
                    <span class="info-text"
                      style="max-width:420px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; display:inline-block; vertical-align:bottom;">
                      {{ rtspForm.url || '未配置' }}
                    </span>
                  </div>
                  <!-- 文件夹信息（当前项） -->
                  <div v-else-if="selectedInputType === 'folder' && currentFolderMeta" class="info-row">
                    <span class="info-text">{{ folderIndex + 1 }} / {{ folderPreviews.length }}</span>
                    <span class="info-sep">|</span>
                    <span class="info-label">文件：</span><span class="info-text">{{ currentFolderMeta.relativePath
                      }}</span>
                    <span class="info-sep">|</span>
                    <span class="info-label">大小：</span><span class="info-text">{{ formatSize(currentFolderMeta.size)
                      }}</span>
                  </div>
                </div>
                <!-- 右侧：操作按钮 -->
                <div class="actions-right">
                  <template v-if="selectedInputType === 'folder' && folderPreviews.length">
                    <el-button size="small" @click="prevImage" :disabled="folderIndex === 0">上一张</el-button>
                    <el-button size="small" type="danger" @click="deleteCurrentImage"
                      :disabled="!folderPreviews.length">删除当前</el-button>
                    <el-button size="small" @click="nextImage"
                      :disabled="folderIndex === folderPreviews.length - 1">下一张</el-button>
                    <el-button size="small" type="warning" @click="resetPreviews">重置</el-button>
                  </template>
                  <template v-else-if="selectedInputType === 'realtime'">
                    <el-button size="small" @click="openRealtimeDialog">重新配置</el-button>
                    <el-button size="small" type="primary" :loading="realtimeTesting"
                      @click="testRtspConnection">测试连接</el-button>
                    <el-button size="small" type="danger" :disabled="!realtimeStreamUrl"
                      @click="stopRealtime">停止</el-button>
                  </template>
                  <template v-else>
                    <el-button size="small" type="warning" @click="resetPreviews">重置</el-button>
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="panel bottom-left">
            <div class="panel-title">
              <span>数据操作</span>
              <div>
                <el-select v-model="selectedProcessType" @change="selectProcessType" class="type-select">
                  <el-option v-for="type in processTypes" :key="type.value" :label="type.label" :value="type.value">
                  </el-option>
                </el-select>
                <el-button type="primary" @click="triggerProcess">预处理</el-button>
                <el-select v-model="selectedDetectType" @change="selectDetectType" class="type-select">
                  <el-option v-for="type in detectTypes" :key="type.value" :label="type.label" :value="type.value">
                  </el-option>
                </el-select>
                <el-button type="primary" @click="triggerDetect">检测识别</el-button>
              </div>
            </div>
            <div class="panel-body">
              <!-- 结果展示区：展示预处理后的结果（图像/视频/文件夹） -->
              <!-- 图像结果 -->
              <div v-if="selectedInputType === 'image'" class="result-block">
                <template v-if="imagePreview">
                  <img :src="imagePreview" alt="预处理图像" class="preview-img" />
                </template>
                <template v-else>
                  <div class="empty-state">
                    <div class="empty-content">
                      <i class="empty-icon">🖼️</i>
                      <h3 class="empty-title">暂无图像结果</h3>
                      <p class="empty-description">点击上方“预处理”生成结果</p>
                    </div>
                  </div>
                </template>
              </div>

              <!-- 视频结果 -->
              <div v-else-if="selectedInputType === 'video'" class="result-block">
                <template v-if="videoPreview">
                  <video :src="videoPreview" class="preview-video" controls autoplay muted playsinline></video>
                </template>
                <template v-else>
                  <div class="empty-state">
                    <div class="empty-content">
                      <i class="empty-icon">🎬</i>
                      <h3 class="empty-title">暂无视频结果</h3>
                      <p class="empty-description">点击上方“预处理”生成结果</p>
                    </div>
                  </div>
                </template>
              </div>

              <!-- 文件夹结果（显示当前索引图片） -->
              <div v-else-if="selectedInputType === 'folder'" class="result-block">
                <template v-if="folderPreviews && folderPreviews.length">
                  <img :src="currentFolderPreview" alt="预处理文件夹图像" class="preview-img" />
                  <div class="folder-meta">{{ folderIndex + 1 }} / {{ folderPreviews.length }}</div>
                </template>
                <template v-else>
                  <div class="empty-state">
                    <div class="empty-content">
                      <i class="empty-icon">📁</i>
                      <h3 class="empty-title">暂无文件夹结果</h3>
                      <p class="empty-description">点击上方“预处理”生成结果</p>
                    </div>
                  </div>
                </template>
              </div>

              <!-- 其他或未选择 -->
              <div v-else class="empty-state">
                <div class="empty-content">
                  <i class="empty-icon">📂</i>
                  <h3 class="empty-title">请选择数据源并预处理</h3>
                  <p class="empty-description">在上方选择类型并点击“预处理”以查看结果</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel right">
          <!-- 右侧区域内容 -->
          <div class="panel-title">右侧区域</div>
          <div class="panel-body">在这里放置主预览/标注画布/检测结果等内容</div>
        </div>
      </div>
      <!-- 实时视频配置对话框 -->
      <el-dialog v-model="realtimeDialogVisible" title="实时视频配置" width="520px" append-to-body>
        <el-form label-width="96px">
          <el-form-item label="RTSP 地址">
            <el-input v-model="rtspForm.url" placeholder="rtsp://user:pass@ip:port/stream" />
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="rtspForm.username" placeholder="可选" />
          </el-form-item>
          <el-form-item label="密码">
            <el-input v-model="rtspForm.password" type="password" placeholder="可选" />
          </el-form-item>
          <el-form-item label="其他参数">
            <el-input v-model="rtspForm.params" placeholder="例如: 解码方式=hw, 延迟=低" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="realtimeDialogVisible = false">取 消</el-button>
            <el-button type="primary" :loading="realtimeTesting" @click="testRtspConnection">测试连接</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'
import { serverAddress } from '@/api/config';
import { APIUploadData } from '@/api/api.js'
export default {
  name: 'DetectedView',
  components: {
    HeaderComponent
  },
  data() {
    return {
      selectedInputType: 'image',
      inputTypes: [
        { value: 'image', label: '图像', icon: '🖼️' },
        { value: 'folder', label: '文件夹', icon: '📁' },
        { value: 'video', label: '视频', icon: '🎥' },
        { value: 'realtime', label: '实时视频', icon: '📡' }
      ],
      selectedProcessType: 'CL1',
      processTypes: [
        { value: 'CL1', label: '处理1' },
        { value: 'CL2', label: '处理2' },
        { value: 'CL3', label: '处理3' },
        { value: 'CL4', label: '处理4' },
        { value: 'CL5', label: '处理5' }
      ],
      selectedDetectType: 'SF1',
      detectTypes: [
        { value: 'SF1', label: '算法1' },
        { value: 'SF2', label: '算法2' },
        { value: 'SF3', label: '算法3' },
        { value: 'SF4', label: '算法4' },
        { value: 'SF5', label: '算法5' }
      ],
      // 预览相关状态
      imagePreview: null, // 单张图像预览URL
      videoPreview: null, // 单个视频预览URL
      folderPreviews: [], // 文件夹内所有图像的本地URL列表 [{url,name,relativePath,size}]
      folderIndex: 0,     // 当前预览索引
      // 元信息
      imageMeta: null,    // {name,size}
      videoMeta: null,    // {name,size}
      // 实时流
      realtimeDialogVisible: false,
      realtimeTesting: false,
      realtimeStreamUrl: '', // 后端返回的可播放流地址（HLS/WebRTC/FLV HTTP）
      rtspForm: {
        url: '',
        username: '',
        password: '',
        params: '', // 其他参数，如解码延迟等
      },
    }
  },
  computed: {
    currentFolderPreview() {
      if (!this.folderPreviews.length) return null
      return this.folderPreviews[this.folderIndex]?.url || null
    },
    currentFolderMeta() {
      if (!this.folderPreviews.length) return null
      const it = this.folderPreviews[this.folderIndex]
      return it ? { name: it.name, size: it.size, relativePath: it.relativePath } : null
    },
    // 是否为 HLS 流
    isHls() {
      const url = this.realtimeStreamUrl || ''
      return /\.m3u8(\?|#|$)/i.test(url)
    }
  },
  watch: {
    // 监听实时流地址变化，自动初始化/销毁 hls 播放器
    realtimeStreamUrl(newVal) {
      if (newVal && this.selectedInputType === 'realtime') {
        this.$nextTick(() => this.setupRealtimePlayer(newVal))
      } else {
        this.destroyRealtimePlayer()
      }
    }
  },
  methods: {
    //#region 基础方法
    goBack() {
      this.$router.go(-1)
    },
    //#endregion
    //#region 数据输入框
    selectInputType(type) {
      const wasRealtime = this.selectedInputType === 'realtime'
      this.selectedInputType = type
      if (type === 'realtime') {
        // 切换到实时前，清理其他预览
        this.resetPreviews()
        this.openRealtimeDialog()
      } else if (wasRealtime) {
        // 离开实时，停止播放
        this.stopRealtime()
      }
    },

    triggerFileUpload() {
      const type = this.selectedInputType
      if (type === 'image') {
        this.$refs.imageInput.click()
      } else if (type === 'folder') {
        this.$refs.folderInput.click()
      } else if (type === 'video') {
        this.$refs.videoInput.click()
      }
    },
    // 实时配置对话框
    openRealtimeDialog() {
      this.realtimeDialogVisible = true
    },
    async testRtspConnection() {
      if (!this.rtspForm.url) {
        this.$message?.warning && this.$message.warning('请输入 RTSP 地址')
        return
      }
      this.realtimeTesting = true
      try {
        // 期望后端返回 { ok: boolean, stream_url?: string, message?: string }
        const resp = await fetch(serverAddress + 'api/detected/rtsp/test', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            url: this.rtspForm.url,
            username: this.rtspForm.username,
            password: this.rtspForm.password,
            params: this.rtspForm.params,
          })
        })
        const data = await resp.json()
        if (resp.ok && data?.ok && data?.stream_url) {
          this.realtimeStreamUrl = data.stream_url
          this.$message?.success && this.$message.success('连接成功')
          this.realtimeDialogVisible = false
        } else {
          const msg = data?.message || '连接失败，请检查RTSP地址或服务端'
          this.$message?.error && this.$message.error(msg)
        }
      } catch (e) {
        this.$message?.error && this.$message.error('连接异常：' + (e?.message || e))
      } finally {
        this.realtimeTesting = false
      }
    },
    stopRealtime() {
      // 停止实时流播放
      this.destroyRealtimePlayer()
      this.realtimeStreamUrl = ''
    },
    async setupRealtimePlayer(url) {
      // 仅对 HLS 进行特殊处理
      if (!this.isHls) return
      try {
        const videoEl = this.$refs.realtimeVideo
        if (!videoEl) return

        // Safari 或部分浏览器原生支持 HLS（直接设置 src）
        const canNativePlayHls = videoEl.canPlayType('application/vnd.apple.mpegURL')
        if (canNativePlayHls) {
          videoEl.src = url
          await videoEl.play().catch(() => { })
          return
        }

        // 动态引入 hls.js，需先安装依赖：npm i hls.js
        const mod = await import(/* webpackChunkName: "hlsjs" */ 'hls.js')
        const Hls = mod.default || mod
        if (Hls && Hls.isSupported()) {
          this._hls && this._hls.destroy()
          const hls = new Hls({ enableWorker: true })
          hls.loadSource(url)
          hls.attachMedia(videoEl)
          hls.on(Hls.Events.MANIFEST_PARSED, async () => {
            try { await videoEl.play() } catch (err) { console.debug('video play after manifest parsed failed:', err) }
          })
          this._hls = hls
        } else {
          // 回退尝试原生
          videoEl.src = url
          await videoEl.play().catch(() => { })
        }
      } catch (e) {
        this.$message?.error && this.$message.error('HLS 初始化失败：' + (e?.message || e))
      }
    },
    destroyRealtimePlayer() {
      try {
        if (this._hls) {
          this._hls.destroy()
          this._hls = null
        }
        const videoEl = this.$refs.realtimeVideo
        if (videoEl) {
          try { videoEl.pause() } catch (err) { console.debug('video pause failed:', err) }
          videoEl.removeAttribute('src')
          videoEl.load?.()
        }
      } catch (err) { console.debug('destroyRealtimePlayer cleanup failed:', err) }
    },
    // 重置并释放旧的对象URL，避免内存泄漏
    resetPreviews() {
      if (this.imagePreview && typeof this.imagePreview === 'string' && this.imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(this.imagePreview)
      }
      if (this.videoPreview && typeof this.videoPreview === 'string' && this.videoPreview.startsWith('blob:')) {
        URL.revokeObjectURL(this.videoPreview)
      }
      if (this.folderPreviews?.length) {
        this.folderPreviews.forEach(it => {
          if (it?.url && typeof it.url === 'string' && it.url.startsWith('blob:')) {
            URL.revokeObjectURL(it.url)
          }
        })
      }
      this.imagePreview = null
      this.videoPreview = null
      this.folderPreviews = []
      this.folderIndex = 0
      this.imageMeta = null
      this.videoMeta = null
      // 同时停止实时播放
      this.realtimeStreamUrl = ''
    },
    // 选择单张图像（上传到后端，再用返回路径预览）
    async handleImageUpload(e) {
      const files = e.target.files
      if (!files || !files.length) return
      this.resetPreviews()
      const file = files[0]
      try {
        const fd = new FormData()
        fd.append('files', file, file.name)
        const resp = await APIUploadData(fd)
        const data = resp?.data || resp // 兼容可能的返回结构
        const list = data?.data?.files || data?.files || []
        if (!list.length) throw new Error('上传失败：未返回文件路径')
        const rel = list[0]
        this.imagePreview = serverAddress + 'api/detected/static/' + rel
        this.imageMeta = { name: file.name, size: file.size, relativePath: rel }
      } catch (err) {
        this.$message?.error && this.$message.error('图像上传失败：' + (err?.message || err))
      } finally {
        e.target.value = ''
      }
    },
    // 选择视频（上传到后端，再用返回路径预览）
    async handleVideoUpload(e) {
      const files = e.target.files
      if (!files || !files.length) return
      this.resetPreviews()
      const file = files[0]
      try {
        const fd = new FormData()
        fd.append('files', file, file.name)
        const resp = await APIUploadData(fd)
        const data = resp?.data || resp
        const list = data?.data?.files || data?.files || []
        if (!list.length) throw new Error('上传失败：未返回文件路径')
        const rel = list[0]
        this.videoPreview = serverAddress + 'api/detected/static/' + rel
        this.videoMeta = { name: file.name, size: file.size, relativePath: rel }
      } catch (err) {
        this.$message?.error && this.$message.error('视频上传失败：' + (err?.message || err))
      } finally {
        e.target.value = ''
      }
    },
    // 选择文件夹（仅图像）（批量上传到后端，再用返回路径预览）
    async handleFilesUpload(e) {
      const files = Array.from(e.target.files || [])
      if (!files.length) return
      this.resetPreviews()
      const imageFiles = files.filter(f => (f.type && f.type.startsWith('image/')) || /\.(png|jpg|jpeg|bmp|gif|webp)$/i.test(f.name))
      if (!imageFiles.length) {
        this.$message?.warning && this.$message.warning('选中的文件夹不包含图像文件')
        e.target.value = ''
        return
      }
      // 按相对路径排序，保证预览顺序
      imageFiles.sort((a, b) => (a.webkitRelativePath || a.name).localeCompare(b.webkitRelativePath || b.name))
      try {
        const fd = new FormData()
        imageFiles.forEach(f => fd.append('files', f, f.name))
        const resp = await APIUploadData(fd)
        const data = resp?.data || resp
        const list = data?.data?.files || data?.files || []
        if (!list.length) throw new Error('上传失败：未返回文件路径')
        // 服务器按接收顺序保存，和我们 append 的顺序一致，进行一一对应
        this.folderPreviews = list.map((rel, idx) => ({
          url: serverAddress + 'api/detected/static/' + rel,
          name: imageFiles[idx]?.name || rel.split('/').pop(),
          size: imageFiles[idx]?.size,
          relativePath: rel
        }))
        this.folderIndex = 0
      } catch (err) {
        this.$message?.error && this.$message.error('文件夹上传失败：' + (err?.message || err))
      } finally {
        e.target.value = ''
      }
    },
    // 上一张/下一张
    prevImage() {
      if (this.folderIndex > 0) this.folderIndex -= 1
    },
    nextImage() {
      if (this.folderIndex < this.folderPreviews.length - 1) this.folderIndex += 1
    },
    // 删除当前图像（文件夹场景）
    deleteCurrentImage() {
      if (!this.folderPreviews.length) return
      const idx = this.folderIndex
      const [removed] = this.folderPreviews.splice(idx, 1)
      if (removed?.url) URL.revokeObjectURL(removed.url)
      if (this.folderIndex >= this.folderPreviews.length) {
        this.folderIndex = Math.max(0, this.folderPreviews.length - 1)
      }
    },
    // 格式化大小
    formatSize(bytes) {
      if (bytes == null) return '-'
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      let num = Number(bytes)
      while (num >= 1024 && i < units.length - 1) {
        num /= 1024
        i++
      }
      return `${num.toFixed(num < 10 && i > 0 ? 2 : 0)} ${units[i]}`
    },
    //#endregion
    //#region 数据操作框
    selectProcessType(type) {
      this.selectedProcessType = type
    },
    selectDetectType(type) {
      this.selectedDetectType = type
    },
    triggerProcess() {

    },
    triggerDetect() {

    }
    //#endregion
  }
  ,
  beforeUnmount() {
    // 组件卸载时清理资源
    try { this.resetPreviews() } catch (err) { console.debug('resetPreviews on unmount failed:', err) }
    try { this.stopRealtime() } catch (err) { console.debug('stopRealtime on unmount failed:', err) }
    try { this.destroyRealtimePlayer() } catch (err) { console.debug('destroyRealtimePlayer on unmount failed:', err) }
  }
}
</script>

<style scoped>
/* 页面整体：纵向布局，内容区占满剩余高度 */
.detected {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-image: url("../assets/imgs/backimg.png");
}

.content {
  flex: 1;
  overflow: hidden;
  /* 防止内部溢出影响整体滚动 */
  padding: 12px;
}

/* 三区域布局：左列（上下各一块） + 右列（整列一块） */
.layout {
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.left {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
  min-height: 0;
  /* 允许子元素正确滚动 */
}

.panel {
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 修复 grid/flex 子项溢出 */
}

.panel-title {
  padding: 10px 12px;
  font-weight: 600;
  color: #4476da;
  border-bottom: 1px solid #ebeef5;
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.panel-title .type-select {
  width: 120px;
  margin-right: 12px;
}

.panel-body {
  padding: 12px;
  /* 改为上下两块：上预览(80%)、下信息(20%) */
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
}

/* 预览/信息区域样式 */
.panel-body-top {
  /* 固定预览高度，防止因图像尺寸放大区域 */
  flex: 0 0 280px;
  /* 可按需调整，例如 240/300 */
  height: 280px;
  overflow: hidden;
}

.panel-body-bottom {
  /* 其余空间用于信息/操作区 */
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  border-top: 1px solid #1d263b;
  padding-top: 8px;
}

.info-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.actions-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-area {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-block {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-img {
  width: 100%;
  height: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 6px;
  background: #000;
}

.preview-video {
  width: 100%;
  height: 100%;
  max-height: 100%;
  border-radius: 6px;
  background: #000;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  color: #606266;
}

.info-text {
  color: #303133;
}

.info-sep {
  color: #c0c4cc;
}

.folder-info {
  color: #606266;
  font-size: 13px;
}

.right {
  min-height: 0;
}

/* 响应式：小屏幕下改为上下堆叠 */
@media (max-width: 1024px) {
  .layout {
    grid-template-columns: 1fr;
    grid-auto-rows: max-content;
  }

  .right {
    order: 3;
  }
}

/* 空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

}

.empty-content {
  text-align: center;
  color: #909399;
  font-size: 13px;
}

.empty-icon {
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.empty-description {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}
</style>
