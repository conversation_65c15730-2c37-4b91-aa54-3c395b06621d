<template>
  <div class="login">
    <HeaderComponent />
    <video autoplay muted loop id="background-video">
      <source src="../assets/video/sky.mp4" type="video/mp4">
      您的浏览器不支持 video 标签。
    </video>
    
    <!-- 登录框 -->
    <div v-if="isLoginVisible" class="login-container">
      <h2>登录</h2>
      <form @submit.prevent="handleLogin">
        <div class="input-group">
          <label for="username">账号:</label>
          <input v-model="username" type="text" id="username" placeholder="请输入账号" />
        </div>
        <div class="input-group">
          <label for="password">密码:</label>
          <input v-model="password" type="password" id="password" placeholder="请输入密码" />
        </div>
        <div class="button-container">
          <button type="submit" class="btn btn-primary">登录</button>
          <button type="button" class="btn btn-secondary" @click="closeLogin">关闭</button>
        </div>
      </form>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </div>

    <!-- 模式选择按钮 -->
    <div class="content">
      <div class="mode-buttons">
        <div class="tab" @click="topage('manager')">
          <div class="ui-tab-icon ui-admin"></div>
          <div class="ui-tab-title">管理员模式</div>
        </div>
        <div class="tab" @click="topage('user')">
          <div class="ui-tab-icon ui-usr"></div>
          <div class="ui-tab-title">游客模式</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'

export default {
  name: 'LoginView',
  components: {
    HeaderComponent
  },
  props: {
    msg: String
  },
  data() {
    return {
      isLoginVisible: false,  // 控制登录框是否显示
      username: '',
      password: '',
      errorMessage: '',
      users: [] // 存储用户信息
    };
  },
  methods: {
    // 点击管理员模式时显示登录框
    topage(mode) {
      if (mode === 'manager') {
        this.isLoginVisible = true;  // 显示登录框
      } else if (mode === 'user') {
        this.$router.push('/user');
      }
    },
    // 关闭登录框
    closeLogin() {
      this.isLoginVisible = false;
      this.username = '';
      this.password = '';
      this.errorMessage = ''; // 清空错误信息
    },
    async fetchUserData() {
      try {
        // 使用 fetch 读取 public 目录下的 userinfo.txt 文件
        const response = await fetch('/conf/userinfo.conf'); // 公共文件在 public 目录下，路径以 '/' 开头
        if (!response.ok) {
          throw new Error('文件读取失败');
        }

        const data = await response.text(); // 获取文件内容作为文本
        const parsedData = JSON.parse(data); // 假设文件内容是 JSON 格式
        this.users = Array.isArray(parsedData) ? parsedData : []; // 确保是数组
      } catch (error) {
        console.error('获取用户数据失败', error);
        this.users = []; // 如果读取失败，确保 users 为空数组
      }
    },

    async handleLogin() {
      // 先读取用户数据
      await this.fetchUserData();

      // 确保 this.users 是数组
      if (!Array.isArray(this.users)) {
        this.errorMessage = '用户数据加载失败';
        return;
      }

      const user = this.users.find(user => user.username === this.username && user.password === this.password);

      if (user) {
        // 登录成功
        this.errorMessage = ''; // 清空错误信息

        // 登录成功，跳转到 index 页面
        this.$router.push({ path: '/manager' });
      } else {
        // 登录失败
        this.errorMessage = '用户名或密码错误';
      }
    },
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
/* 主容器样式 */
.login {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 背景视频 */
#background-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  transform: translate(-50%, -50%);
}

/* 主要内容区域 */
.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 模式按钮容器 */
.mode-buttons {
  display: flex;
  gap: 300px;
  align-items: center;
  justify-content: center;
}

/* 单个按钮样式 */
.tab {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 260px;
  height: 329px;
  color: #fff;
  text-align: center;
  border-radius: 10px;
  background-image: url("../assets/imgs/home-item.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.tab:hover {
  width: 279px;
  height: 343px;
  transform: scale(1.05);
}

/* 按钮图标样式 */
.ui-tab-icon {
  width: 110px;
  height: 101px;
  margin-bottom: 20px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.ui-usr {
  background-image: url("../assets/imgs/usr.png");
}

.ui-admin {
  background-image: url("../assets/imgs/admin.png");
}

/* 按钮标题样式 */
.ui-tab-title {
  font-size: 24px;
  color: #a2cfff;
  font-weight: bold;
  letter-spacing: 2px;
}

/* 登录框样式 */
.login-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 400px;
  width: 90%;
  padding: 30px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.login-container h2 {
  text-align: center;
  font-size: 24px;
  color: #333;
  margin-bottom: 25px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  font-size: 14px;
  color: #555;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.input-group input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.button-container {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.btn {
  flex: 1;
  padding: 12px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
  transform: translateY(-1px);
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  text-align: center;
  margin-top: 15px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mode-buttons {
    flex-direction: column;
    gap: 20px;
  }
  
  .tab {
    width: 220px;
    height: 280px;
  }
  
  .tab:hover {
    width: 235px;
    height: 295px;
  }
  
  .ui-tab-icon {
    width: 90px;
    height: 85px;
    margin-bottom: 15px;
  }
  
  .ui-tab-title {
    font-size: 20px;
  }
  
  .login-container {
    width: 95%;
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .tab {
    width: 180px;
    height: 240px;
  }
  
  .tab:hover {
    width: 190px;
    height: 250px;
  }
  
  .ui-tab-icon {
    width: 70px;
    height: 65px;
    margin-bottom: 10px;
  }
  
  .ui-tab-title {
    font-size: 18px;
  }
  
  .login-container {
    padding: 15px;
  }
  
  .login-container h2 {
    font-size: 20px;
  }
}
</style>


