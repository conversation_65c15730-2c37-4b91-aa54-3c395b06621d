<template>
  <div class="detected">
    <HeaderComponent title="智能判读" :showBackButton="true" @back="goBack" />
    <!-- 主体三分区布局：左上、左下、右侧 -->
    <div class="content">
      <div class="left">
        <div class="pane top-left">
          <!-- 左上区域内容 -->
          <div class="pane-title">
            <h3>数据输入</h3>
            <div class="operation">
              <el-select v-model="selectedType" placeholder="请选择" @change="handleInputTypeChange">
                <el-option v-for="item in InputOptions" :key="item.value" :label="item.label" :value="item.value">
                  {{ item.label }}
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="pane-body">
              
          </div>
        </div>
        <div class="pane bottom-left">
          <!-- 左下区域内容 -->
          <div class="pane-title">
            <h3>左下区域</h3>
            <el-button type="primary">主要按钮</el-button>
          </div>
          <div class="pane-body"></div>
        </div>
      </div>
      <div class="pane right">
        <!-- 右侧区域内容 -->
        <div class="pane-title">右侧区域</div>
        <div class="pane-body"></div>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'

export default {
  name: 'DetectedView',
  components: {
    HeaderComponent
  },
  data() {
    return {
      InputOptions: [
        {
          value: 'image',
          label: '图像'
        },
        {
          value: 'video',
          label: '视频'
        },
        {
          value: 'files',
          label: '文件夹'
        }
      ],
      selectedType: 'image'
    }

  },
  methods: {

    // ==================== 原有方法 ====================
    goBack() {
      this.$router.go(-1)
    },
    handleInputTypeChange(type) {
      this.selectedType = type;
    }
  },

}
</script>

<style scoped>
@import '@/assets/css/variables.css';

.detected {
  overflow: hidden;
  background-size: cover;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-image: url("../assets/imgs/backimg.png");
  display: flex;
  flex-direction: column;
}

/* 主体区域占满 Header 之外的空间 */
.content {
  flex: 1;
  min-height: 0;
  /* 避免子元素溢出 */
  display: flex;
  gap: 12px;
  padding: 12px;
  box-sizing: border-box;
}

/* 左侧列（包含左上/左下） */
.left {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 50%;
  min-width: 360px;
}

/* 右侧全高区域 */
.right {
  flex: 1;
}

/* 面板基础样式 */
.pane {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(6px);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.top-left,
.bottom-left {
  flex: 1;
  /* 左上与左下均分高度 */
}

.pane-title {
  font-weight: 600;
  padding: 10px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  color: #333;
  display: flex;
  justify-content: space-between;
  /* 关键属性 */
  align-items: center;
  /* 可选，使子元素垂直居中 */
}

.operation {
  width: 100px;
}

.pane-body {
  flex: 1;
  min-height: 0;
  padding: 12px;
  overflow: auto;
}

/* 响应式：窄屏下改为上下排列 */
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
  }

  .left,
  .right {
    width: 100%;
    min-width: 0;
  }
}
</style>
