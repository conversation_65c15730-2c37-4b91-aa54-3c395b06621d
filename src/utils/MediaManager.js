/**
 * 媒体资源管理器
 * 统一管理图片、视频等媒体资源，优化内存使用
 */
class MediaManager {
  constructor(options = {}) {
    this.maxCacheSize = options.maxCacheSize || 50 * 1024 * 1024 // 50MB
    this.maxImageSize = options.maxImageSize || 10 * 1024 * 1024 // 10MB
    this.maxVideoSize = options.maxVideoSize || 100 * 1024 * 1024 // 100MB
    this.compressionQuality = options.compressionQuality || 0.8
    
    // 资源缓存
    this.imageCache = new Map()
    this.videoCache = new Map()
    this.urlCache = new Set()
    
    // 当前缓存大小
    this.currentCacheSize = 0
    
    // 支持的文件类型
    this.supportedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    this.supportedVideoTypes = ['video/mp4', 'video/webm', 'video/ogg']
  }

  /**
   * 处理图片文件
   * @param {File} file - 图片文件
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async processImage(file, options = {}) {
    try {
      // 验证文件类型
      if (!this.supportedImageTypes.includes(file.type)) {
        throw new Error(`不支持的图片格式: ${file.type}`)
      }

      // 验证文件大小
      if (file.size > this.maxImageSize) {
        throw new Error(`图片文件过大: ${this.formatFileSize(file.size)}，最大支持: ${this.formatFileSize(this.maxImageSize)}`)
      }

      const fileKey = this.generateFileKey(file)
      
      // 检查缓存
      if (this.imageCache.has(fileKey)) {
        return this.imageCache.get(fileKey)
      }

      // 压缩图片（如果需要）
      const processedFile = await this.compressImage(file, options)
      
      // 创建URL
      const url = URL.createObjectURL(processedFile.file || file)
      this.urlCache.add(url)

      // 获取图片信息
      const imageInfo = await this.getImageInfo(url)
      
      const result = {
        url,
        file: processedFile.file || file,
        originalFile: file,
        info: imageInfo,
        compressed: processedFile.compressed,
        size: (processedFile.file || file).size,
        timestamp: Date.now()
      }

      // 添加到缓存
      this.addToCache('image', fileKey, result)
      
      return result
    } catch (error) {
      console.error('图片处理失败:', error)
      throw error
    }
  }

  /**
   * 处理视频文件
   * @param {File} file - 视频文件
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async processVideo(file) {
    try {
      // 验证文件类型
      if (!this.supportedVideoTypes.includes(file.type)) {
        throw new Error(`不支持的视频格式: ${file.type}`)
      }

      // 验证文件大小
      if (file.size > this.maxVideoSize) {
        throw new Error(`视频文件过大: ${this.formatFileSize(file.size)}，最大支持: ${this.formatFileSize(this.maxVideoSize)}`)
      }

      const fileKey = this.generateFileKey(file)
      
      // 检查缓存
      if (this.videoCache.has(fileKey)) {
        return this.videoCache.get(fileKey)
      }

      // 创建URL
      const url = URL.createObjectURL(file)
      this.urlCache.add(url)

      // 获取视频信息
      const videoInfo = await this.getVideoInfo(url)
      
      const result = {
        url,
        file,
        info: videoInfo,
        size: file.size,
        timestamp: Date.now()
      }

      // 添加到缓存
      this.addToCache('video', fileKey, result)
      
      return result
    } catch (error) {
      console.error('视频处理失败:', error)
      throw error
    }
  }

  /**
   * 压缩图片
   * @param {File} file - 原始图片文件
   * @param {Object} options - 压缩选项
   * @returns {Promise<Object>} 压缩结果
   */
  async compressImage(file, options = {}) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // 计算压缩后的尺寸
        const maxWidth = options.maxWidth || 1920
        const maxHeight = options.maxHeight || 1080
        let { width, height } = img
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }
        
        canvas.width = width
        canvas.height = height
        
        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height)
        
        // 转换为Blob
        canvas.toBlob((blob) => {
          if (blob && blob.size < file.size) {
            // 压缩成功
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: file.lastModified
            })
            resolve({ file: compressedFile, compressed: true })
          } else {
            // 压缩后反而更大，使用原文件
            resolve({ file: file, compressed: false })
          }
        }, file.type, this.compressionQuality)
      }
      
      img.onerror = () => {
        resolve({ file: file, compressed: false })
      }
      
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 获取图片信息
   * @param {string} url - 图片URL
   * @returns {Promise<Object>} 图片信息
   */
  async getImageInfo(url) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
          aspectRatio: img.naturalWidth / img.naturalHeight
        })
      }
      img.onerror = reject
      img.src = url
    })
  }

  /**
   * 获取视频信息
   * @param {string} url - 视频URL
   * @returns {Promise<Object>} 视频信息
   */
  async getVideoInfo(url) {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.onloadedmetadata = () => {
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration,
          aspectRatio: video.videoWidth / video.videoHeight
        })
      }
      video.onerror = reject
      video.src = url
    })
  }

  /**
   * 生成文件唯一标识
   * @param {File} file - 文件对象
   * @returns {string} 文件标识
   */
  generateFileKey(file) {
    return `${file.name}-${file.size}-${file.lastModified}`
  }

  /**
   * 添加到缓存
   * @param {string} type - 缓存类型 ('image' | 'video')
   * @param {string} key - 缓存键
   * @param {Object} data - 缓存数据
   */
  addToCache(type, key, data) {
    const cache = type === 'image' ? this.imageCache : this.videoCache
    
    // 检查缓存大小
    this.currentCacheSize += data.size
    if (this.currentCacheSize > this.maxCacheSize) {
      this.cleanupCache()
    }
    
    cache.set(key, data)
  }

  /**
   * 清理缓存
   */
  cleanupCache() {
    const allItems = [
      ...Array.from(this.imageCache.entries()).map(([key, value]) => ({ key, value, type: 'image' })),
      ...Array.from(this.videoCache.entries()).map(([key, value]) => ({ key, value, type: 'video' }))
    ]
    
    // 按时间戳排序，删除最旧的项目
    allItems.sort((a, b) => a.value.timestamp - b.value.timestamp)
    
    while (this.currentCacheSize > this.maxCacheSize * 0.8 && allItems.length > 0) {
      const item = allItems.shift()
      this.removeFromCache(item.type, item.key)
    }
  }

  /**
   * 从缓存中移除项目
   * @param {string} type - 缓存类型
   * @param {string} key - 缓存键
   */
  removeFromCache(type, key) {
    const cache = type === 'image' ? this.imageCache : this.videoCache
    const item = cache.get(key)
    
    if (item) {
      // 释放URL对象
      if (this.urlCache.has(item.url)) {
        URL.revokeObjectURL(item.url)
        this.urlCache.delete(item.url)
      }
      
      this.currentCacheSize -= item.size
      cache.delete(key)
    }
  }

  /**
   * 清理特定文件的缓存
   * @param {File} file - 要清理缓存的文件
   */
  clearFileCache(file) {
    const fileKey = this.generateFileKey(file)
    this.removeFromCache('image', fileKey)
    this.removeFromCache('video', fileKey)
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    // 释放所有URL对象
    for (const url of this.urlCache) {
      URL.revokeObjectURL(url)
    }

    // 清空缓存
    this.imageCache.clear()
    this.videoCache.clear()
    this.urlCache.clear()
    this.currentCacheSize = 0
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      imageCount: this.imageCache.size,
      videoCount: this.videoCache.size,
      totalUrls: this.urlCache.size,
      currentCacheSize: this.currentCacheSize,
      maxCacheSize: this.maxCacheSize,
      cacheUsagePercent: (this.currentCacheSize / this.maxCacheSize * 100).toFixed(2)
    }
  }
}

export default MediaManager


