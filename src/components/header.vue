<template>
  <div class="header">
    <div class="head">
      <!-- 左侧日期显示 -->
      <div class="date-display">
        <i class="date-icon">📅</i>
        <span>{{ currentDate }}</span>
      </div>
      
      <!-- 中间内容区域 -->
      <div class="center-content">
        <img class="headbot" src="../assets/imgs/toptitle.png" />
        <div class="title">{{ dynamicTitle || '无人机光电图像智能判读原型系统' }}</div>
      </div>
      
      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 时间显示 -->
        <div class="time-display">
          <i class="time-icon">🕐</i>
          <span>{{ currentTime }}</span>
        </div>
        
        <!-- 返回按钮 -->
        <div v-if="showBackButton" class="back-button" @click="handleBack">
          <i class="back-icon">←</i>
          <span>返回</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HeaderComponent',
  props: {
    msg: String,
    title: {
      type: String,
      default: ''
    },
    showBackButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentDate: '',
      currentTime: '',
      timer: null
    }
  },
  computed: {
    dynamicTitle() {
      return this.title || '无人机光电图像智能判读原型系统'
    }
  },
  mounted() {
    this.updateDateTime()
    this.timer = setInterval(this.updateDateTime, 1000)
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateDateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      
      this.currentDate = `${year}-${month}-${day}`
      this.currentTime = `${hours}:${minutes}:${seconds}`
    },
    handleBack() {
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
.header {
  background-color: #242f42;
  padding: 15px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 0 auto;
  position: relative;
}

/* 左侧日期显示 */
.date-display {
  display: flex;
  align-items: center;
  color: #e0e6ed;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  min-width: 140px;
}

.date-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 中间内容区域 */
.center-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin: 0 20px;
}

.headbot {
  position: absolute;
  height: 50px;
  width: auto;
  margin-bottom: 8px;
  bottom: -50px;
}

.title {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 右侧区域 */
.right-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 右侧时间显示 */
.time-display {
  display: flex;
  align-items: center;
  color: #e0e6ed;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  min-width: 100px;
}

.time-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 返回按钮样式 */
.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e0e6ed;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  will-change: transform, background-color;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.back-button:active {
  transform: scale(0.98);
}

.back-icon {
  margin-right: 5px;
  font-size: 16px;
  font-weight: bold;
}

/* 响应式设计调整 */
@media (max-width: 768px) {
  .head {
    flex-direction: column;
    gap: 15px;
  }
  
  .right-section {
    width: 100%;
    justify-content: space-between;
  }
  
  .back-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .back-button {
    padding: 5px 10px;
    font-size: 11px;
  }
  
  .back-icon {
    font-size: 14px;
  }
}
</style>
